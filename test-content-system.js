#!/usr/bin/env node

/**
 * 测试新的多格式内容处理系统
 */

const { ContentDetector } = require('./src/lib/content/detector.ts')
const { defaultContentRouter } = require('./src/lib/content/router.ts')

// 测试用例
const testCases = [
  {
    name: 'URL检测',
    input: 'https://www.example.com',
    expectedType: 'url'
  },
  {
    name: 'Markdown检测',
    input: '# 标题\n\n这是一个**粗体**文本，包含[链接](https://example.com)。\n\n- 列表项1\n- 列表项2',
    expectedType: 'markdown'
  },
  {
    name: '纯文本检测',
    input: '这是一段普通的文本内容，没有特殊格式。',
    expectedType: 'text'
  },
  {
    name: '复杂Markdown检测',
    input: '```javascript\nconsole.log("Hello World")\n```\n\n> 这是引用\n\n| 表格 | 内容 |\n|------|------|\n| 行1  | 数据 |',
    expectedType: 'markdown'
  }
]

async function runTests() {
  console.log('🚀 开始测试多格式内容处理系统\n')
  
  let passedTests = 0
  let totalTests = testCases.length
  
  for (const testCase of testCases) {
    console.log(`📝 测试: ${testCase.name}`)
    console.log(`输入: ${testCase.input.substring(0, 50)}${testCase.input.length > 50 ? '...' : ''}`)
    
    try {
      // 测试内容检测
      const detectionResult = ContentDetector.detect(testCase.input)
      console.log(`检测结果: ${detectionResult.type} (置信度: ${detectionResult.confidence.toFixed(2)})`)
      
      const passed = detectionResult.type === testCase.expectedType
      console.log(`结果: ${passed ? '✅ 通过' : '❌ 失败'}`)
      
      if (passed) {
        passedTests++
      } else {
        console.log(`期望: ${testCase.expectedType}, 实际: ${detectionResult.type}`)
      }
      
      // 测试内容处理（仅对简单案例）
      if (testCase.name === '纯文本检测') {
        console.log('测试内容处理...')
        const processResult = await defaultContentRouter.processContent(testCase.input)
        console.log(`处理结果: ${processResult.success ? '✅ 成功' : '❌ 失败'}`)
        if (processResult.success) {
          console.log(`处理时间: ${processResult.processingTime}ms`)
        }
      }
      
    } catch (error) {
      console.log(`❌ 错误: ${error.message}`)
    }
    
    console.log('---\n')
  }
  
  console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`)
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！')
    process.exit(0)
  } else {
    console.log('⚠️  部分测试失败')
    process.exit(1)
  }
}

// 运行测试
runTests().catch(error => {
  console.error('测试运行失败:', error)
  process.exit(1)
})
