import { ContentType, ContentDetectionResult } from './types'

/**
 * 内容类型检测器
 * 智能识别输入内容的类型
 */
export class ContentDetector {
  
  /**
   * 检测URL类型
   */
  private static detectURL(input: string): ContentDetectionResult {
    // 改进的URL检测正则表达式
    const urlRegex = /^https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$/i
    const simpleUrlRegex = /^https?:\/\/.+/i
    
    if (urlRegex.test(input.trim())) {
      return {
        type: ContentType.URL,
        confidence: 0.95,
        metadata: {
          protocol: input.startsWith('https') ? 'https' : 'http',
          domain: this.extractDomain(input)
        }
      }
    }
    
    if (simpleUrlRegex.test(input.trim())) {
      return {
        type: ContentType.URL,
        confidence: 0.8,
        metadata: {
          protocol: input.startsWith('https') ? 'https' : 'http',
          domain: this.extractDomain(input)
        }
      }
    }
    
    return {
      type: ContentType.URL,
      confidence: 0
    }
  }
  
  /**
   * 检测Markdown类型
   */
  private static detectMarkdown(input: string): ContentDetectionResult {
    const markdownPatterns = [
      /^#{1,6}\s+.+$/m,           // 标题 # ## ###
      /^\*{1,2}[^*]+\*{1,2}$/m,   // 粗体/斜体 *text* **text**
      /^[-*+]\s+.+$/m,            // 无序列表 - * +
      /^\d+\.\s+.+$/m,            // 有序列表 1. 2.
      /\[.+\]\(.+\)/,             // 链接 [text](url)
      /!\[.*\]\(.+\)/,            // 图片 ![alt](url)
      /^```[\s\S]*?```$/m,        // 代码块 ```
      /`[^`]+`/,                  // 行内代码 `code`
      /^\|.+\|$/m,                // 表格 |col1|col2|
      /^>{1,}\s+.+$/m,            // 引用 > text
      /^---+$/m,                  // 分隔线 ---
    ]
    
    let matchCount = 0
    const totalPatterns = markdownPatterns.length
    
    for (const pattern of markdownPatterns) {
      if (pattern.test(input)) {
        matchCount++
      }
    }
    
    const confidence = matchCount / totalPatterns
    
    // 如果匹配度超过阈值，认为是Markdown
    if (confidence > 0.2 || matchCount >= 2) {
      return {
        type: ContentType.MARKDOWN,
        confidence: Math.min(confidence * 2, 0.95), // 放大置信度但不超过0.95
        metadata: {
          matchedPatterns: matchCount,
          totalPatterns
        }
      }
    }
    
    return {
      type: ContentType.MARKDOWN,
      confidence: 0
    }
  }
  
  /**
   * 检测纯文本类型
   */
  private static detectText(input: string): ContentDetectionResult {
    // 纯文本是默认类型，总是有基础置信度
    const hasMultipleLines = input.includes('\n')
    const hasSpecialChars = /[<>&"']/.test(input)
    
    let confidence = 0.3 // 基础置信度
    
    if (hasMultipleLines) confidence += 0.2
    if (!hasSpecialChars) confidence += 0.2
    
    return {
      type: ContentType.TEXT,
      confidence: Math.min(confidence, 0.8), // 文本类型最高置信度为0.8
      metadata: {
        hasMultipleLines,
        hasSpecialChars,
        length: input.length
      }
    }
  }
  
  /**
   * 从URL中提取域名
   */
  private static extractDomain(url: string): string {
    try {
      return new URL(url).hostname
    } catch {
      return ''
    }
  }
  
  /**
   * 主检测方法
   * 返回最可能的内容类型
   */
  static detect(input: string): ContentDetectionResult {
    if (!input || !input.trim()) {
      return {
        type: ContentType.TEXT,
        confidence: 0,
        metadata: { empty: true }
      }
    }
    
    const trimmedInput = input.trim()
    
    // 按优先级检测各种类型
    const detectionResults = [
      this.detectURL(trimmedInput),
      this.detectMarkdown(trimmedInput),
      this.detectText(trimmedInput)
    ]
    
    // 找到置信度最高的类型
    const bestMatch = detectionResults.reduce((best, current) => 
      current.confidence > best.confidence ? current : best
    )
    
    return bestMatch
  }
  
  /**
   * 获取所有检测结果（用于调试）
   */
  static detectAll(input: string): ContentDetectionResult[] {
    const trimmedInput = input.trim()
    
    return [
      this.detectURL(trimmedInput),
      this.detectMarkdown(trimmedInput),
      this.detectText(trimmedInput)
    ].sort((a, b) => b.confidence - a.confidence)
  }
  
  /**
   * 强制检测为指定类型（用于用户手动指定）
   */
  static forceDetect(input: string, type: ContentType): ContentDetectionResult {
    return {
      type,
      confidence: 1.0,
      metadata: { forced: true }
    }
  }
}
