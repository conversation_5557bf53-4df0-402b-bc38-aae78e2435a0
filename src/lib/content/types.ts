import React from 'react'

/**
 * 支持的内容类型枚举
 */
export enum ContentType {
  URL = 'url',
  MARKDOWN = 'markdown',
  TEXT = 'text',
  // 预留未来扩展
  AUDIO = 'audio',
  VIDEO = 'video',
  PDF = 'pdf'
}

/**
 * 内容数据接口
 */
export interface ContentData {
  type: ContentType
  source: string // 原始输入内容
  title?: string
  content?: string // 处理后的内容
  metadata?: Record<string, any>
}

/**
 * 内容检测结果
 */
export interface ContentDetectionResult {
  type: ContentType
  confidence: number // 检测置信度 0-1
  metadata?: Record<string, any>
}

/**
 * 内容处理结果
 */
export interface ProcessedContent {
  data: ContentData
  success: boolean
  error?: string
  processingTime?: number
}

/**
 * 内容渲染器属性接口
 */
export interface ContentRendererProps {
  content: ContentData
  loading?: boolean
  error?: string
  onLoadComplete?: () => void
  onError?: (error: string) => void
}

/**
 * 基础内容处理器接口
 */
export interface ContentProcessor {
  readonly type: ContentType
  
  /**
   * 检测内容是否属于此处理器类型
   */
  detect(input: string): ContentDetectionResult
  
  /**
   * 处理内容
   */
  process(input: string): Promise<ProcessedContent>
  
  /**
   * 获取对应的渲染组件
   */
  getRenderer(): React.ComponentType<ContentRendererProps>
}

/**
 * 内容路由器配置
 */
export interface ContentRouterConfig {
  processors: ContentProcessor[]
  defaultType?: ContentType
  enableCache?: boolean
}

/**
 * 内容处理选项
 */
export interface ContentProcessingOptions {
  forceType?: ContentType
  enableAI?: boolean
  enableCache?: boolean
  timeout?: number
}

/**
 * 兼容现有系统的类型映射
 */
export const LegacyTypeMapping = {
  'url': ContentType.URL,
  'text': ContentType.TEXT
} as const

export type LegacyContentType = keyof typeof LegacyTypeMapping
