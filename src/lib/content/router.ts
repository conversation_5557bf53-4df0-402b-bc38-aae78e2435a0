import { 
  ContentType, 
  ContentProcessor, 
  ContentData, 
  ProcessedContent, 
  ContentRouterConfig,
  ContentProcessingOptions 
} from './types'
import { ContentDetector } from './detector'
import { ProcessorRegistry, createDefaultProcessors } from './processors'

/**
 * 内容路由器
 * 负责根据内容类型选择合适的处理器并执行处理
 */
export class ContentRouter {
  private processors: Map<ContentType, ContentProcessor>
  private config: ContentRouterConfig
  private cache: Map<string, ProcessedContent> = new Map()
  
  constructor(config?: Partial<ContentRouterConfig>) {
    this.config = {
      processors: createDefaultProcessors(),
      defaultType: ContentType.TEXT,
      enableCache: true,
      ...config
    }
    
    // 初始化处理器映射
    this.processors = new Map()
    this.config.processors.forEach(processor => {
      this.processors.set(processor.type, processor)
    })
  }
  
  /**
   * 处理内容的主要方法
   */
  async processContent(
    input: string, 
    options?: ContentProcessingOptions
  ): Promise<ProcessedContent> {
    const startTime = Date.now()
    
    try {
      // 验证输入
      if (!input || !input.trim()) {
        throw new Error('输入内容不能为空')
      }
      
      // 检查缓存
      if (this.config.enableCache && options?.enableCache !== false) {
        const cached = this.getCachedResult(input)
        if (cached) {
          return cached
        }
      }
      
      // 确定内容类型
      const contentType = this.determineContentType(input, options)
      
      // 获取对应的处理器
      const processor = this.getProcessor(contentType)
      if (!processor) {
        throw new Error(`不支持的内容类型: ${contentType}`)
      }
      
      // 处理内容
      const result = await this.executeProcessor(processor, input, options)
      
      // 添加路由器元数据
      result.data.metadata = {
        ...result.data.metadata,
        routerProcessingTime: Date.now() - startTime,
        detectedType: contentType,
        processorUsed: processor.constructor.name,
        cached: false
      }
      
      // 缓存结果
      if (this.config.enableCache && result.success) {
        this.setCachedResult(input, result)
      }
      
      return result
      
    } catch (error) {
      console.error('内容路由处理失败:', error)
      
      // 返回错误结果
      return {
        data: {
          type: this.config.defaultType || ContentType.TEXT,
          source: input,
          content: '',
          title: '处理失败',
          metadata: {
            error: error instanceof Error ? error.message : String(error),
            processingTime: Date.now() - startTime
          }
        },
        success: false,
        error: error instanceof Error ? error.message : String(error),
        processingTime: Date.now() - startTime
      }
    }
  }
  
  /**
   * 确定内容类型
   */
  private determineContentType(
    input: string, 
    options?: ContentProcessingOptions
  ): ContentType {
    // 如果强制指定了类型，直接使用
    if (options?.forceType) {
      return options.forceType
    }
    
    // 使用检测器自动检测
    const detectionResult = ContentDetector.detect(input)
    
    // 如果检测置信度太低，使用默认类型
    if (detectionResult.confidence < 0.3) {
      return this.config.defaultType || ContentType.TEXT
    }
    
    return detectionResult.type
  }
  
  /**
   * 获取处理器
   */
  private getProcessor(type: ContentType): ContentProcessor | null {
    return this.processors.get(type) || null
  }
  
  /**
   * 执行处理器
   */
  private async executeProcessor(
    processor: ContentProcessor,
    input: string,
    options?: ContentProcessingOptions
  ): Promise<ProcessedContent> {
    // 设置超时
    const timeout = options?.timeout || 30000 // 默认30秒
    
    return Promise.race([
      processor.process(input),
      new Promise<ProcessedContent>((_, reject) => {
        setTimeout(() => reject(new Error('处理超时')), timeout)
      })
    ])
  }
  
  /**
   * 获取缓存结果
   */
  private getCachedResult(input: string): ProcessedContent | null {
    const cacheKey = this.generateCacheKey(input)
    const cached = this.cache.get(cacheKey)
    
    if (cached) {
      // 更新缓存标记
      cached.data.metadata = {
        ...cached.data.metadata,
        cached: true,
        cacheHitAt: new Date().toISOString()
      }
    }
    
    return cached || null
  }
  
  /**
   * 设置缓存结果
   */
  private setCachedResult(input: string, result: ProcessedContent): void {
    const cacheKey = this.generateCacheKey(input)
    
    // 限制缓存大小
    if (this.cache.size >= 100) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    
    this.cache.set(cacheKey, result)
  }
  
  /**
   * 生成缓存键
   */
  private generateCacheKey(input: string): string {
    // 使用简单的哈希算法
    let hash = 0
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return hash.toString(36)
  }
  
  /**
   * 清空缓存
   */
  clearCache(): void {
    this.cache.clear()
  }
  
  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
  
  /**
   * 添加处理器
   */
  addProcessor(processor: ContentProcessor): void {
    this.processors.set(processor.type, processor)
  }
  
  /**
   * 移除处理器
   */
  removeProcessor(type: ContentType): boolean {
    return this.processors.delete(type)
  }
  
  /**
   * 获取所有支持的类型
   */
  getSupportedTypes(): ContentType[] {
    return Array.from(this.processors.keys())
  }
  
  /**
   * 检查是否支持某种类型
   */
  supports(type: ContentType): boolean {
    return this.processors.has(type)
  }
}

/**
 * 默认内容路由器实例
 */
export const defaultContentRouter = new ContentRouter()

/**
 * 便捷的内容处理函数
 */
export async function processContent(
  input: string,
  options?: ContentProcessingOptions
): Promise<ProcessedContent> {
  return defaultContentRouter.processContent(input, options)
}
