// 导出所有处理器
export { BaseContentProcessor, BaseProcessorFactory } from './base'
export { URLProcessor, URLProcessorFactory } from './url'
export { MarkdownProcessor, MarkdownProcessorFactory } from './markdown'
export { TextProcessor, TextProcessorFactory } from './text'

// 导出处理器类型
export * from '../types'

import { ContentType, ContentProcessor } from '../types'
import { URLProcessor } from './url'
import { MarkdownProcessor } from './markdown'
import { TextProcessor } from './text'

/**
 * 处理器注册表
 * 管理所有可用的内容处理器
 */
export class ProcessorRegistry {
  private static processors: Map<ContentType, () => ContentProcessor> = new Map()
  
  /**
   * 注册处理器
   */
  static register(type: ContentType, factory: () => ContentProcessor): void {
    this.processors.set(type, factory)
  }
  
  /**
   * 获取处理器
   */
  static get(type: ContentType): ContentProcessor | null {
    const factory = this.processors.get(type)
    return factory ? factory() : null
  }
  
  /**
   * 获取所有注册的处理器类型
   */
  static getRegisteredTypes(): ContentType[] {
    return Array.from(this.processors.keys())
  }
  
  /**
   * 检查是否支持某种类型
   */
  static supports(type: ContentType): boolean {
    return this.processors.has(type)
  }
  
  /**
   * 获取所有处理器实例
   */
  static getAllProcessors(): ContentProcessor[] {
    return Array.from(this.processors.values()).map(factory => factory())
  }
  
  /**
   * 清空注册表
   */
  static clear(): void {
    this.processors.clear()
  }
}

/**
 * 默认处理器配置
 * 注册所有内置处理器
 */
export function registerDefaultProcessors(): void {
  ProcessorRegistry.register(ContentType.URL, () => new URLProcessor())
  ProcessorRegistry.register(ContentType.MARKDOWN, () => new MarkdownProcessor())
  ProcessorRegistry.register(ContentType.TEXT, () => new TextProcessor())
}

/**
 * 创建默认处理器实例
 */
export function createDefaultProcessors(): ContentProcessor[] {
  return [
    new URLProcessor(),
    new MarkdownProcessor(),
    new TextProcessor()
  ]
}

/**
 * 根据类型创建处理器
 */
export function createProcessor(type: ContentType): ContentProcessor | null {
  switch (type) {
    case ContentType.URL:
      return new URLProcessor()
    case ContentType.MARKDOWN:
      return new MarkdownProcessor()
    case ContentType.TEXT:
      return new TextProcessor()
    default:
      return null
  }
}

// 自动注册默认处理器
registerDefaultProcessors()
