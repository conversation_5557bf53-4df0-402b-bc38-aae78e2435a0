import React from 'react'
import { BaseContentProcessor } from './base'
import {
  ContentType,
  ContentDetectionResult,
  ProcessedContent,
  ContentRendererProps
} from '../types'

// 动态导入scraper以避免客户端导入服务器端模块
const getScraper = async () => {
  if (typeof window !== 'undefined') {
    // 客户端环境，不导入scraper
    throw new Error('URL处理器只能在服务器端使用')
  }
  const { scrapeWebPage } = await import('@/lib/scraper')
  return { scrapeWebPage }
}

/**
 * URL内容处理器
 * 处理网页链接，集成现有的scrapeWebPage逻辑
 */
export class URLProcessor extends BaseContentProcessor {
  readonly type = ContentType.URL
  
  /**
   * 检测是否为URL
   */
  detect(input: string): ContentDetectionResult {
    const trimmed = input.trim()
    
    // 严格的URL检测
    const strictUrlRegex = /^https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$/i
    // 宽松的URL检测
    const looseUrlRegex = /^https?:\/\/.+/i
    
    if (strictUrlRegex.test(trimmed)) {
      return {
        type: ContentType.URL,
        confidence: 0.95,
        metadata: {
          protocol: trimmed.startsWith('https') ? 'https' : 'http',
          domain: this.extractDomain(trimmed),
          strict: true
        }
      }
    }
    
    if (looseUrlRegex.test(trimmed)) {
      return {
        type: ContentType.URL,
        confidence: 0.8,
        metadata: {
          protocol: trimmed.startsWith('https') ? 'https' : 'http',
          domain: this.extractDomain(trimmed),
          strict: false
        }
      }
    }
    
    return {
      type: ContentType.URL,
      confidence: 0
    }
  }
  
  /**
   * 处理URL内容
   */
  async process(input: string): Promise<ProcessedContent> {
    if (!this.validateInput(input)) {
      return this.handleError(new Error('无效的URL输入'), input)
    }

    const url = input.trim()

    try {
      const { result: scrapedData, time } = await this.withTiming(async () => {
        const { scrapeWebPage } = await getScraper()
        return await scrapeWebPage(url)
      })

      const contentData = this.createContentData(
        url,
        scrapedData.content,
        scrapedData.title,
        {
          textContent: scrapedData.textContent,
          domain: this.extractDomain(url),
          scrapedAt: new Date().toISOString(),
          contentLength: scrapedData.content?.length || 0,
          textLength: scrapedData.textContent?.length || 0
        }
      )

      return this.createProcessedContent(contentData, true, undefined, time)

    } catch (error) {
      console.error('URL处理失败:', error)
      return this.handleError(error, input)
    }
  }
  
  /**
   * 获取URL渲染组件
   */
  getRenderer(): React.ComponentType<ContentRendererProps> {
    // 动态导入避免循环依赖
    return React.lazy(() => import('@/components/content/BrowserRenderer'))
  }
  
  /**
   * 从URL提取域名
   */
  private extractDomain(url: string): string {
    try {
      return new URL(url).hostname
    } catch {
      // 如果URL解析失败，尝试简单的正则提取
      const match = url.match(/https?:\/\/([^\/\?#]+)/i)
      return match ? match[1] : ''
    }
  }
  
  /**
   * 验证URL格式
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }
  
  /**
   * 生成URL的默认标题
   */
  protected generateDefaultTitle(input: string): string {
    const domain = this.extractDomain(input)
    return domain || '网页内容'
  }
}

/**
 * URL处理器工厂
 */
export class URLProcessorFactory {
  static create(): URLProcessor {
    return new URLProcessor()
  }
  
  static supports(type: ContentType): boolean {
    return type === ContentType.URL
  }
}
