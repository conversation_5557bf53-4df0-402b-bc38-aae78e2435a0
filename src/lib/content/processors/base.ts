import React from 'react'
import { 
  ContentType, 
  ContentProcessor, 
  ContentData, 
  ProcessedContent, 
  ContentDetectionResult,
  ContentRendererProps 
} from '../types'

/**
 * 抽象基础内容处理器
 * 提供通用的处理逻辑和接口实现
 */
export abstract class BaseContentProcessor implements ContentProcessor {
  abstract readonly type: ContentType
  
  /**
   * 抽象方法：检测内容类型
   */
  abstract detect(input: string): ContentDetectionResult
  
  /**
   * 抽象方法：处理内容
   */
  abstract process(input: string): Promise<ProcessedContent>
  
  /**
   * 抽象方法：获取渲染组件
   */
  abstract getRenderer(): React.ComponentType<ContentRendererProps>
  
  /**
   * 通用的内容数据创建方法
   */
  protected createContentData(
    input: string, 
    processedContent?: string, 
    title?: string,
    metadata?: Record<string, any>
  ): ContentData {
    return {
      type: this.type,
      source: input,
      content: processedContent || input,
      title: title || this.generateDefaultTitle(input),
      metadata: {
        processedAt: new Date().toISOString(),
        processor: this.constructor.name,
        ...metadata
      }
    }
  }
  
  /**
   * 通用的处理结果创建方法
   */
  protected createProcessedContent(
    data: ContentData,
    success: boolean = true,
    error?: string,
    processingTime?: number
  ): ProcessedContent {
    return {
      data,
      success,
      error,
      processingTime
    }
  }
  
  /**
   * 生成默认标题
   */
  protected generateDefaultTitle(input: string): string {
    const maxLength = 50
    const trimmed = input.trim()
    
    if (trimmed.length <= maxLength) {
      return trimmed
    }
    
    return trimmed.substring(0, maxLength) + '...'
  }
  
  /**
   * 通用的错误处理
   */
  protected handleError(error: any, input: string): ProcessedContent {
    const errorMessage = error instanceof Error ? error.message : String(error)
    
    return this.createProcessedContent(
      this.createContentData(input, '', '处理失败', { error: errorMessage }),
      false,
      errorMessage
    )
  }
  
  /**
   * 验证输入内容
   */
  protected validateInput(input: string): boolean {
    return typeof input === 'string' && input.trim().length > 0
  }
  
  /**
   * 计算处理时间的装饰器方法
   */
  protected async withTiming<T>(operation: () => Promise<T>): Promise<{ result: T; time: number }> {
    const startTime = Date.now()
    const result = await operation()
    const time = Date.now() - startTime
    
    return { result, time }
  }
}

/**
 * 处理器工厂接口
 */
export interface ProcessorFactory {
  create(): ContentProcessor
  supports(type: ContentType): boolean
}

/**
 * 抽象处理器工厂
 */
export abstract class BaseProcessorFactory implements ProcessorFactory {
  abstract create(): ContentProcessor
  abstract supports(type: ContentType): boolean
}
