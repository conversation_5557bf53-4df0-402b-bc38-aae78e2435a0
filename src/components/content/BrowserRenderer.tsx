'use client'

import React from 'react'
import { ContentRendererProps } from '@/lib/content/types'
import LightBrowser from '@/components/ui/LightBrowser'

/**
 * 浏览器渲染器
 * 包装现有的LightBrowser组件，用于渲染URL内容
 */
export default function BrowserRenderer({
  content,
  loading = false,
  error,
  onLoadComplete,
  onError
}: ContentRendererProps) {
  
  // 错误状态
  if (error) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-white rounded-lg border border-red-200">
        <div className="text-6xl text-red-300 mb-4">🌐</div>
        <div className="text-center">
          <p className="text-lg font-medium text-red-600">网页加载失败</p>
          <p className="text-sm text-red-500 mt-2 max-w-md">{error}</p>
        </div>
      </div>
    )
  }
  
  // 加载状态
  if (loading) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-white rounded-lg border border-gray-200">
        <div className="flex space-x-2 mb-4">
          <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce"></div>
          <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        <p className="text-gray-600">正在加载网页...</p>
      </div>
    )
  }
  
  // 验证URL
  if (!content.source) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-white rounded-lg border border-gray-200">
        <div className="text-6xl text-gray-300 mb-4">🌐</div>
        <div className="text-center">
          <p className="text-lg font-medium text-gray-600">无效的URL</p>
          <p className="text-sm text-gray-500 mt-2">请提供有效的网页链接</p>
        </div>
      </div>
    )
  }
  
  // 处理LightBrowser的错误回调
  const handleBrowserError = (browserError: string) => {
    console.error('浏览器组件错误:', browserError)
    onError?.(browserError)
  }
  
  // 处理LightBrowser的加载完成回调
  const handleBrowserLoadComplete = () => {
    console.log('浏览器内容加载完成:', content.source)
    onLoadComplete?.()
  }
  
  return (
    <div className="h-full bg-white rounded-lg overflow-hidden">
      {/* 内容信息栏 */}
      <div className="px-4 py-2 bg-gray-50 border-b border-gray-200 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-sm text-gray-600 font-medium">
            {content.title || '网页内容'}
          </span>
        </div>
        
        {/* 显示域名 */}
        {content.metadata?.domain && (
          <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
            {content.metadata.domain}
          </div>
        )}
      </div>
      
      {/* 浏览器内容 */}
      <div className="h-full" style={{ height: 'calc(100% - 48px)' }}>
        <LightBrowser
          url={content.source}
          title={content.title || '网页内容'}
          onLoadComplete={handleBrowserLoadComplete}
          onError={handleBrowserError}
        />
      </div>
      
      {/* 元数据信息（开发模式下显示） */}
      {process.env.NODE_ENV === 'development' && content.metadata && (
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs p-2 rounded max-w-xs">
          <div>类型: {content.type}</div>
          {content.metadata.protocol && (
            <div>协议: {content.metadata.protocol}</div>
          )}
          {content.metadata.processingTime && (
            <div>处理时间: {content.metadata.processingTime}ms</div>
          )}
          {content.metadata.cached && (
            <div>缓存: 是</div>
          )}
        </div>
      )}
    </div>
  )
}

/**
 * 浏览器渲染器的错误边界组件
 */
export class BrowserRendererErrorBoundary extends React.Component<
  { children: React.ReactNode; onError?: (error: Error) => void },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode; onError?: (error: Error) => void }) {
    super(props)
    this.state = { hasError: false }
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('BrowserRenderer错误:', error, errorInfo)
    this.props.onError?.(error)
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div className="h-full flex flex-col items-center justify-center bg-white rounded-lg border border-red-200">
          <div className="text-6xl text-red-300 mb-4">💥</div>
          <div className="text-center">
            <p className="text-lg font-medium text-red-600">浏览器组件崩溃</p>
            <p className="text-sm text-red-500 mt-2 max-w-md">
              {this.state.error?.message || '浏览器渲染器发生未知错误'}
            </p>
          </div>
        </div>
      )
    }
    
    return this.props.children
  }
}

/**
 * 带错误边界的浏览器渲染器
 */
export function SafeBrowserRenderer(props: ContentRendererProps) {
  return (
    <BrowserRendererErrorBoundary onError={props.onError}>
      <BrowserRenderer {...props} />
    </BrowserRendererErrorBoundary>
  )
}
