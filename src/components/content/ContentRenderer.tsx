'use client'

import React, { Suspense } from 'react'
import { ContentType, ContentData, ContentRendererProps } from '@/lib/content/types'

// 动态导入渲染组件
const BrowserRenderer = React.lazy(() => import('./BrowserRenderer'))
const MarkdownRenderer = React.lazy(() => import('./MarkdownRenderer'))
const TextRenderer = React.lazy(() => import('./TextRenderer'))

/**
 * 加载状态组件
 */
function LoadingState() {
  return (
    <div className="flex flex-col items-center justify-center h-96 space-y-4">
      <div className="flex space-x-2">
        <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce"></div>
        <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
        <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
      </div>
      <p className="text-gray-600">正在加载内容...</p>
    </div>
  )
}

/**
 * 错误状态组件
 */
function ErrorState({ error }: { error: string }) {
  return (
    <div className="flex flex-col items-center justify-center h-96 space-y-4">
      <div className="text-6xl text-red-300">⚠️</div>
      <div className="text-center">
        <p className="text-lg font-medium text-red-600">内容加载失败</p>
        <p className="text-sm text-red-500 mt-2 max-w-md">{error}</p>
      </div>
    </div>
  )
}

/**
 * 空状态组件
 */
function EmptyState() {
  return (
    <div className="flex flex-col items-center justify-center h-96 space-y-4">
      <div className="text-6xl text-gray-300">📄</div>
      <div className="text-center">
        <p className="text-lg font-medium text-gray-600">暂无内容</p>
        <p className="text-sm text-gray-500 mt-2">
          输入链接或粘贴文本开始处理
        </p>
      </div>
    </div>
  )
}

/**
 * 统一内容渲染器
 * 根据内容类型选择合适的渲染组件
 */
interface ContentRendererMainProps {
  content?: ContentData | null
  loading?: boolean
  error?: string
  onLoadComplete?: () => void
  onError?: (error: string) => void
  className?: string
}

export default function ContentRenderer({
  content,
  loading = false,
  error,
  onLoadComplete,
  onError,
  className = ''
}: ContentRendererMainProps) {
  
  // 错误状态
  if (error) {
    return (
      <div className={`h-full bg-white rounded-lg border border-red-200 ${className}`}>
        <ErrorState error={error} />
      </div>
    )
  }
  
  // 加载状态
  if (loading) {
    return (
      <div className={`h-full bg-white rounded-lg border border-gray-200 ${className}`}>
        <LoadingState />
      </div>
    )
  }
  
  // 空状态
  if (!content) {
    return (
      <div className={`h-full bg-white rounded-lg border border-gray-200 ${className}`}>
        <EmptyState />
      </div>
    )
  }
  
  // 根据内容类型选择渲染组件
  const renderContent = () => {
    const rendererProps: ContentRendererProps = {
      content,
      loading,
      error,
      onLoadComplete,
      onError
    }
    
    switch (content.type) {
      case ContentType.URL:
        return <BrowserRenderer {...rendererProps} />
      
      case ContentType.MARKDOWN:
        return <MarkdownRenderer {...rendererProps} />
      
      case ContentType.TEXT:
        return <TextRenderer {...rendererProps} />
      
      default:
        // 如果没有找到对应的渲染器，使用文本渲染器作为后备
        return <TextRenderer {...rendererProps} />
    }
  }
  
  return (
    <div className={`h-full ${className}`}>
      <Suspense fallback={<LoadingState />}>
        {renderContent()}
      </Suspense>
    </div>
  )
}

/**
 * 内容渲染器的高阶组件
 * 提供错误边界和性能监控
 */
export class ContentRendererErrorBoundary extends React.Component<
  { children: React.ReactNode; onError?: (error: Error) => void },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode; onError?: (error: Error) => void }) {
    super(props)
    this.state = { hasError: false }
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ContentRenderer错误:', error, errorInfo)
    this.props.onError?.(error)
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div className="h-full bg-white rounded-lg border border-red-200">
          <ErrorState error={this.state.error?.message || '渲染组件发生未知错误'} />
        </div>
      )
    }
    
    return this.props.children
  }
}

/**
 * 带错误边界的内容渲染器
 */
export function SafeContentRenderer(props: ContentRendererMainProps) {
  return (
    <ContentRendererErrorBoundary onError={props.onError}>
      <ContentRenderer {...props} />
    </ContentRendererErrorBoundary>
  )
}
