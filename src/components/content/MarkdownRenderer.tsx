'use client'

import React, { useEffect, useState } from 'react'
import ReactMarkdown from 'react-markdown'
import { ContentRendererProps } from '@/lib/content/types'
import { Download, Share2, FileText } from 'lucide-react'

/**
 * Markdown渲染器
 * 使用react-markdown和@tailwindcss/typography进行专业的Markdown渲染
 */
export default function MarkdownRenderer({
  content,
  loading = false,
  error,
  onLoadComplete,
  onError
}: ContentRendererProps) {
  const [isRendering, setIsRendering] = useState(true)
  
  useEffect(() => {
    if (content && !loading && !error) {
      // 模拟渲染完成
      const timer = setTimeout(() => {
        setIsRendering(false)
        onLoadComplete?.()
      }, 100)
      
      return () => clearTimeout(timer)
    }
  }, [content, loading, error, onLoadComplete])
  
  // 错误状态
  if (error) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-white rounded-lg border border-red-200">
        <div className="text-6xl text-red-300 mb-4">📝</div>
        <div className="text-center">
          <p className="text-lg font-medium text-red-600">Markdown渲染失败</p>
          <p className="text-sm text-red-500 mt-2 max-w-md">{error}</p>
        </div>
      </div>
    )
  }
  
  // 加载状态
  if (loading || isRendering) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-white rounded-lg border border-gray-200">
        <div className="flex space-x-2 mb-4">
          <div className="w-3 h-3 bg-purple-500 rounded-full animate-bounce"></div>
          <div className="w-3 h-3 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-3 h-3 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        <p className="text-gray-600">正在渲染Markdown...</p>
      </div>
    )
  }
  
  // 验证内容
  if (!content.content && !content.source) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-white rounded-lg border border-gray-200">
        <div className="text-6xl text-gray-300 mb-4">📝</div>
        <div className="text-center">
          <p className="text-lg font-medium text-gray-600">无Markdown内容</p>
          <p className="text-sm text-gray-500 mt-2">请提供有效的Markdown文本</p>
        </div>
      </div>
    )
  }
  
  const markdownContent = content.content || content.source
  
  // 处理导出功能
  const handleExport = () => {
    try {
      const blob = new Blob([markdownContent], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${content.title || 'markdown-content'}.md`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      console.error('导出失败:', err)
      onError?.('导出失败')
    }
  }
  
  // 处理分享功能
  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: content.title || 'Markdown文档',
          text: markdownContent.substring(0, 200) + '...',
        })
      } else {
        // 复制到剪贴板
        await navigator.clipboard.writeText(markdownContent)
        // 这里可以显示一个提示
        console.log('内容已复制到剪贴板')
      }
    } catch (err) {
      console.error('分享失败:', err)
      onError?.('分享失败')
    }
  }
  
  return (
    <div className="h-full flex flex-col bg-white rounded-lg overflow-hidden">
      {/* 顶部工具栏 */}
      <div className="px-4 py-3 bg-gradient-to-r from-purple-50 to-blue-50 border-b border-gray-200 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
            <FileText className="w-4 h-4 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">
              {content.title || 'Markdown文档'}
            </h1>
            {content.metadata && (
              <div className="flex items-center space-x-2 text-xs text-gray-500">
                {content.metadata.wordCount && (
                  <span>{content.metadata.wordCount} 词</span>
                )}
                {content.metadata.lineCount && (
                  <span>• {content.metadata.lineCount} 行</span>
                )}
                {content.metadata.processingTime && (
                  <span>• {content.metadata.processingTime}ms</span>
                )}
              </div>
            )}
          </div>
        </div>
        
        {/* 工具按钮 */}
        <div className="flex items-center space-x-2">
          <button
            onClick={handleShare}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            title="分享"
          >
            <Share2 className="w-4 h-4" />
          </button>
          <button
            onClick={handleExport}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            title="导出"
          >
            <Download className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      {/* Markdown内容 */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          <ReactMarkdown
            className="prose prose-slate prose-lg max-w-none
              prose-headings:text-gray-900 prose-headings:font-semibold prose-headings:tracking-tight
              prose-h1:text-3xl prose-h1:mb-6 prose-h1:mt-0 prose-h1:border-b prose-h1:border-gray-200 prose-h1:pb-4
              prose-h2:text-2xl prose-h2:mb-4 prose-h2:mt-8 prose-h2:border-b prose-h2:border-gray-200 prose-h2:pb-2
              prose-h3:text-xl prose-h3:mb-3 prose-h3:mt-6
              prose-h4:text-lg prose-h4:mb-2 prose-h4:mt-4
              prose-p:text-gray-900 prose-p:leading-relaxed prose-p:mb-4 prose-p:text-base
              prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline
              prose-strong:text-gray-900 prose-strong:font-semibold
              prose-em:text-gray-700 prose-em:italic
              prose-code:text-purple-600 prose-code:bg-purple-50 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-sm prose-code:font-mono
              prose-pre:bg-gray-100 prose-pre:border prose-pre:border-gray-200 prose-pre:rounded-lg prose-pre:p-4 prose-pre:overflow-x-auto
              prose-blockquote:border-l-4 prose-blockquote:border-blue-500 prose-blockquote:bg-blue-50 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:italic
              prose-ul:list-disc prose-ul:pl-6 prose-ul:mb-4
              prose-ol:list-decimal prose-ol:pl-6 prose-ol:mb-4
              prose-li:mb-1 prose-li:text-gray-900
              prose-img:rounded-lg prose-img:shadow-md prose-img:border prose-img:border-gray-200 prose-img:max-w-full prose-img:h-auto
              prose-hr:border-gray-200 prose-hr:my-8 prose-hr:border-t-2
              prose-table:border-collapse prose-table:border prose-table:border-gray-200 prose-table:rounded-lg prose-table:overflow-hidden prose-table:w-full
              prose-th:border prose-th:border-gray-200 prose-th:bg-gray-100 prose-th:p-3 prose-th:font-semibold prose-th:text-left
              prose-td:border prose-td:border-gray-200 prose-td:p-3 prose-td:align-top
              [&>*:first-child]:mt-0
              [&>*:last-child]:mb-0"
          >
            {markdownContent}
          </ReactMarkdown>
        </div>
      </div>
      
      {/* 元数据信息（开发模式下显示） */}
      {process.env.NODE_ENV === 'development' && content.metadata && (
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs p-2 rounded max-w-xs">
          <div>类型: {content.type}</div>
          {content.metadata.hasHeaders && <div>包含标题: 是</div>}
          {content.metadata.hasCodeBlocks && <div>包含代码: 是</div>}
          {content.metadata.hasLinks && <div>包含链接: 是</div>}
          {content.metadata.hasTables && <div>包含表格: 是</div>}
          {content.metadata.cached && <div>缓存: 是</div>}
        </div>
      )}
    </div>
  )
}
