'use client'

import React, { useEffect, useState } from 'react'
import { ContentRendererProps } from '@/lib/content/types'
import { Download, Share2, FileText, Copy } from 'lucide-react'

/**
 * 文本渲染器
 * 用于渲染纯文本内容，提供基础的格式化和交互功能
 */
export default function TextRenderer({
  content,
  loading = false,
  error,
  onLoadComplete,
  onError
}: ContentRendererProps) {
  const [isRendering, setIsRendering] = useState(true)
  const [copied, setCopied] = useState(false)
  
  useEffect(() => {
    if (content && !loading && !error) {
      // 模拟渲染完成
      const timer = setTimeout(() => {
        setIsRendering(false)
        onLoadComplete?.()
      }, 50)
      
      return () => clearTimeout(timer)
    }
  }, [content, loading, error, onLoadComplete])
  
  // 错误状态
  if (error) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-white rounded-lg border border-red-200">
        <div className="text-6xl text-red-300 mb-4">📄</div>
        <div className="text-center">
          <p className="text-lg font-medium text-red-600">文本渲染失败</p>
          <p className="text-sm text-red-500 mt-2 max-w-md">{error}</p>
        </div>
      </div>
    )
  }
  
  // 加载状态
  if (loading || isRendering) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-white rounded-lg border border-gray-200">
        <div className="flex space-x-2 mb-4">
          <div className="w-3 h-3 bg-green-500 rounded-full animate-bounce"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-3 h-3 bg-green-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        <p className="text-gray-600">正在渲染文本...</p>
      </div>
    )
  }
  
  // 验证内容
  if (!content.content && !content.source) {
    return (
      <div className="h-full flex flex-col items-center justify-center bg-white rounded-lg border border-gray-200">
        <div className="text-6xl text-gray-300 mb-4">📄</div>
        <div className="text-center">
          <p className="text-lg font-medium text-gray-600">无文本内容</p>
          <p className="text-sm text-gray-500 mt-2">请提供有效的文本内容</p>
        </div>
      </div>
    )
  }
  
  const textContent = content.content || content.source
  
  // 处理复制功能
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(textContent)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('复制失败:', err)
      onError?.('复制失败')
    }
  }
  
  // 处理导出功能
  const handleExport = () => {
    try {
      const blob = new Blob([textContent], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${content.title || 'text-content'}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      console.error('导出失败:', err)
      onError?.('导出失败')
    }
  }
  
  // 处理分享功能
  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: content.title || '文本内容',
          text: textContent.substring(0, 200) + (textContent.length > 200 ? '...' : ''),
        })
      } else {
        // 复制到剪贴板作为后备
        await handleCopy()
      }
    } catch (err) {
      console.error('分享失败:', err)
      onError?.('分享失败')
    }
  }
  
  // 格式化文本内容
  const formatTextContent = (text: string) => {
    // 将换行符转换为段落
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim())
    
    if (paragraphs.length <= 1) {
      // 单段落或短文本，保持原始换行
      return text.split('\n').map((line, index) => (
        <React.Fragment key={index}>
          {line}
          {index < text.split('\n').length - 1 && <br />}
        </React.Fragment>
      ))
    }
    
    // 多段落文本
    return paragraphs.map((paragraph, index) => (
      <p key={index} className="mb-4 last:mb-0">
        {paragraph.split('\n').map((line, lineIndex) => (
          <React.Fragment key={lineIndex}>
            {line}
            {lineIndex < paragraph.split('\n').length - 1 && <br />}
          </React.Fragment>
        ))}
      </p>
    ))
  }
  
  return (
    <div className="h-full flex flex-col bg-white rounded-lg overflow-hidden">
      {/* 顶部工具栏 */}
      <div className="px-4 py-3 bg-gradient-to-r from-green-50 to-blue-50 border-b border-gray-200 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
            <FileText className="w-4 h-4 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-semibold text-gray-900">
              {content.title || '文本内容'}
            </h1>
            {content.metadata && (
              <div className="flex items-center space-x-2 text-xs text-gray-500">
                {content.metadata.wordCount && (
                  <span>{content.metadata.wordCount} 词</span>
                )}
                {content.metadata.lineCount && (
                  <span>• {content.metadata.lineCount} 行</span>
                )}
                {content.metadata.characterCount && (
                  <span>• {content.metadata.characterCount} 字符</span>
                )}
                {content.metadata.processingTime && (
                  <span>• {content.metadata.processingTime}ms</span>
                )}
              </div>
            )}
          </div>
        </div>
        
        {/* 工具按钮 */}
        <div className="flex items-center space-x-2">
          <button
            onClick={handleCopy}
            className={`p-2 rounded-lg transition-colors ${
              copied 
                ? 'text-green-600 bg-green-100' 
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
            }`}
            title={copied ? '已复制' : '复制'}
          >
            <Copy className="w-4 h-4" />
          </button>
          <button
            onClick={handleShare}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            title="分享"
          >
            <Share2 className="w-4 h-4" />
          </button>
          <button
            onClick={handleExport}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
            title="导出"
          >
            <Download className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      {/* 文本内容 */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          <div className="text-gray-900 leading-relaxed text-base font-mono whitespace-pre-wrap">
            {formatTextContent(textContent)}
          </div>
        </div>
      </div>
      
      {/* 复制成功提示 */}
      {copied && (
        <div className="absolute top-16 right-4 bg-green-500 text-white px-3 py-2 rounded-lg shadow-lg text-sm">
          ✓ 已复制到剪贴板
        </div>
      )}
      
      {/* 元数据信息（开发模式下显示） */}
      {process.env.NODE_ENV === 'development' && content.metadata && (
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs p-2 rounded max-w-xs">
          <div>类型: {content.type}</div>
          {content.metadata.isPlainText !== undefined && (
            <div>纯文本: {content.metadata.isPlainText ? '是' : '否'}</div>
          )}
          {content.metadata.hasMultipleLines !== undefined && (
            <div>多行: {content.metadata.hasMultipleLines ? '是' : '否'}</div>
          )}
          {content.metadata.avgLineLength && (
            <div>平均行长: {content.metadata.avgLineLength}</div>
          )}
          {content.metadata.cached && <div>缓存: 是</div>}
        </div>
      )}
    </div>
  )
}
