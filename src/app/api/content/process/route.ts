import { NextRequest, NextResponse } from 'next/server'
import { defaultContentRouter } from '@/lib/content/router'
import { ContentType } from '@/lib/content/types'

/**
 * 新的内容处理API端点
 * 使用新的多格式内容处理系统
 */
export async function POST(request: NextRequest) {
  try {
    const { input, type, enableAI = true, enableCache = true } = await request.json()
    
    if (!input || !input.trim()) {
      return NextResponse.json(
        { error: '输入内容不能为空', success: false },
        { status: 400 }
      )
    }
    
    // 验证内容类型
    const validTypes = Object.values(ContentType)
    if (type && !validTypes.includes(type)) {
      return NextResponse.json(
        { error: `无效的内容类型: ${type}`, success: false },
        { status: 400 }
      )
    }
    
    console.log('处理内容请求:', { input: input.substring(0, 100), type, enableAI, enableCache })
    
    // 使用内容路由器处理
    const result = await defaultContentRouter.processContent(input, {
      forceType: type,
      enableAI,
      enableCache,
      timeout: 30000 // 30秒超时
    })
    
    if (result.success) {
      console.log('内容处理成功:', {
        type: result.data.type,
        title: result.data.title,
        contentLength: result.data.content?.length || 0,
        processingTime: result.processingTime
      })
      
      return NextResponse.json({
        success: true,
        data: result.data,
        processingTime: result.processingTime,
        cached: result.data.metadata?.cached || false
      })
    } else {
      console.error('内容处理失败:', result.error)
      
      return NextResponse.json(
        { 
          error: result.error || '内容处理失败', 
          success: false,
          processingTime: result.processingTime
        },
        { status: 500 }
      )
    }
    
  } catch (error) {
    console.error('API处理错误:', error)
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '服务器内部错误',
        success: false 
      },
      { status: 500 }
    )
  }
}

/**
 * 获取支持的内容类型
 */
export async function GET() {
  try {
    const supportedTypes = defaultContentRouter.getSupportedTypes()
    
    return NextResponse.json({
      success: true,
      supportedTypes,
      cacheStats: defaultContentRouter.getCacheStats()
    })
  } catch (error) {
    console.error('获取支持类型失败:', error)
    
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '服务器内部错误',
        success: false 
      },
      { status: 500 }
    )
  }
}
