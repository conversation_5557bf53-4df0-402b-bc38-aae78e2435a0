<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容处理系统测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-case {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            background: #f9f9f9;
        }
        .test-input {
            background: #fff;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 8px;
            margin: 8px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .test-result {
            margin: 8px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>🚀 多格式内容处理系统测试</h1>
    
    <div class="test-case">
        <h3>手动测试</h3>
        <textarea id="testInput" placeholder="输入要测试的内容..." style="width: 100%; height: 100px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"></textarea>
        <br>
        <button onclick="testContent()">检测内容类型</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div id="results"></div>

    <div class="test-case">
        <h3>预设测试用例</h3>
        <button onclick="runAllTests()">运行所有测试</button>
    </div>

    <script>
        // 模拟内容检测逻辑（简化版）
        function detectContentType(input) {
            const trimmed = input.trim();
            
            // URL检测
            const urlRegex = /^https?:\/\/(?:[-\w.])+(?:\:[0-9]+)?(?:\/(?:[\w\/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$/i;
            const simpleUrlRegex = /^https?:\/\/.+/i;
            
            if (urlRegex.test(trimmed)) {
                return { type: 'url', confidence: 0.95, reason: '严格URL格式匹配' };
            }
            if (simpleUrlRegex.test(trimmed)) {
                return { type: 'url', confidence: 0.8, reason: '简单URL格式匹配' };
            }
            
            // Markdown检测
            const markdownPatterns = [
                { pattern: /^#{1,6}\s+.+$/m, weight: 0.3, name: '标题' },
                { pattern: /^\*{1,2}[^*\n]+\*{1,2}$/m, weight: 0.2, name: '粗体/斜体' },
                { pattern: /^[-*+]\s+.+$/m, weight: 0.25, name: '无序列表' },
                { pattern: /^\d+\.\s+.+$/m, weight: 0.25, name: '有序列表' },
                { pattern: /\[.+\]\(.+\)/, weight: 0.3, name: '链接' },
                { pattern: /!\[.*\]\(.+\)/, weight: 0.25, name: '图片' },
                { pattern: /^```[\s\S]*?```$/m, weight: 0.4, name: '代码块' },
                { pattern: /`[^`\n]+`/, weight: 0.15, name: '行内代码' },
                { pattern: /^\|.+\|$/m, weight: 0.35, name: '表格' },
                { pattern: /^>{1,}\s+.+$/m, weight: 0.2, name: '引用' }
            ];
            
            let totalWeight = 0;
            const matchedPatterns = [];
            
            for (const { pattern, weight, name } of markdownPatterns) {
                if (pattern.test(trimmed)) {
                    totalWeight += weight;
                    matchedPatterns.push(name);
                }
            }
            
            if (totalWeight > 0.2 || matchedPatterns.length >= 2) {
                return { 
                    type: 'markdown', 
                    confidence: Math.min(totalWeight, 0.95),
                    reason: `匹配Markdown模式: ${matchedPatterns.join(', ')}`
                };
            }
            
            // 默认为文本
            return { type: 'text', confidence: 0.6, reason: '默认文本类型' };
        }

        function testContent() {
            const input = document.getElementById('testInput').value;
            if (!input.trim()) {
                alert('请输入要测试的内容');
                return;
            }
            
            const result = detectContentType(input);
            displayResult('手动测试', input, result);
        }

        function displayResult(testName, input, result) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-case';
            
            resultDiv.innerHTML = `
                <h4>${testName}</h4>
                <div class="test-input">${input.substring(0, 200)}${input.length > 200 ? '...' : ''}</div>
                <div class="test-result info">
                    <strong>检测结果:</strong> ${result.type}<br>
                    <strong>置信度:</strong> ${(result.confidence * 100).toFixed(1)}%<br>
                    <strong>原因:</strong> ${result.reason}
                </div>
            `;
            
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('testInput').value = '';
        }

        function runAllTests() {
            clearResults();
            
            const testCases = [
                {
                    name: 'URL测试 - HTTPS',
                    input: 'https://www.example.com/path?query=value#fragment',
                    expected: 'url'
                },
                {
                    name: 'URL测试 - HTTP',
                    input: 'http://localhost:3000',
                    expected: 'url'
                },
                {
                    name: 'Markdown测试 - 标题和列表',
                    input: '# 主标题\n\n## 副标题\n\n- 列表项1\n- 列表项2\n\n**粗体文本**',
                    expected: 'markdown'
                },
                {
                    name: 'Markdown测试 - 代码块',
                    input: '```javascript\nconsole.log("Hello World");\n```\n\n这是一个代码示例。',
                    expected: 'markdown'
                },
                {
                    name: 'Markdown测试 - 链接和图片',
                    input: '这是一个[链接](https://example.com)和一张![图片](image.jpg)。',
                    expected: 'markdown'
                },
                {
                    name: '纯文本测试',
                    input: '这是一段普通的文本内容，没有任何特殊的格式标记。它只是简单的文字描述。',
                    expected: 'text'
                },
                {
                    name: '复杂Markdown测试',
                    input: '# API文档\n\n## 概述\n\n这是一个RESTful API。\n\n### 端点\n\n| 方法 | 路径 | 描述 |\n|------|------|------|\n| GET | /api/users | 获取用户列表 |\n\n```bash\ncurl -X GET https://api.example.com/users\n```\n\n> 注意：需要认证',
                    expected: 'markdown'
                }
            ];
            
            let passed = 0;
            let total = testCases.length;
            
            testCases.forEach(testCase => {
                const result = detectContentType(testCase.input);
                const success = result.type === testCase.expected;
                
                if (success) passed++;
                
                const resultDiv = document.createElement('div');
                resultDiv.className = 'test-case';
                resultDiv.innerHTML = `
                    <h4>${testCase.name} ${success ? '✅' : '❌'}</h4>
                    <div class="test-input">${testCase.input.substring(0, 150)}${testCase.input.length > 150 ? '...' : ''}</div>
                    <div class="test-result ${success ? 'success' : 'error'}">
                        <strong>期望:</strong> ${testCase.expected}<br>
                        <strong>实际:</strong> ${result.type}<br>
                        <strong>置信度:</strong> ${(result.confidence * 100).toFixed(1)}%<br>
                        <strong>原因:</strong> ${result.reason}
                    </div>
                `;
                
                document.getElementById('results').appendChild(resultDiv);
            });
            
            // 显示总结
            const summaryDiv = document.createElement('div');
            summaryDiv.className = `test-case ${passed === total ? 'success' : 'error'}`;
            summaryDiv.innerHTML = `
                <h3>测试总结</h3>
                <p><strong>通过:</strong> ${passed}/${total} (${(passed/total*100).toFixed(1)}%)</p>
                ${passed === total ? '<p>🎉 所有测试通过！</p>' : '<p>⚠️ 部分测试失败，请检查实现。</p>'}
            `;
            
            document.getElementById('results').appendChild(summaryDiv);
        }
    </script>
</body>
</html>
